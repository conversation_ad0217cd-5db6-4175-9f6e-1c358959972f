import * as React from "react";
import { SVGProps } from "react";
const Instagram = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={20} cy={20} r={20} fill="#fff" fillOpacity={0.2} />
    <g clipPath="url(#a)" fill="#fff">
      <path d="M24.002 16.894a.9.9 0 1 0 0-******* 0 0 0 0 1.8M20 16.148a3.852 3.852 0 1 0 0 7.704 3.852 3.852 0 0 0 0-7.704m0 6.352a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5" />
      <path d="M20 13.851c2.003 0 2.24.008 3.03.044a4.2 4.2 0 0 1 1.393.258 2.5 2.5 0 0 1 1.424 1.424c.165.446.252.917.258 1.392.036.791.044 1.028.044 3.031s-.008 2.24-.044 3.03a4.2 4.2 0 0 1-.258 1.393 2.48 2.48 0 0 1-1.424 1.424 4.2 4.2 0 0 1-1.392.258c-.791.036-1.028.044-3.031.044s-2.24-.008-3.03-.044a4.2 4.2 0 0 1-1.393-.258 2.48 2.48 0 0 1-1.424-1.424 4.2 4.2 0 0 1-.258-1.392c-.036-.791-.044-1.028-.044-3.03 0-2.004.008-2.24.044-3.032a4.2 4.2 0 0 1 .258-1.392 2.48 2.48 0 0 1 1.424-1.424 4.2 4.2 0 0 1 1.392-.258c.791-.036 1.028-.044 3.031-.044m0-1.351c-2.037 0-2.292.009-3.092.045a5.5 5.5 0 0 0-1.82.349 3.83 3.83 0 0 0-2.194 2.194 5.5 5.5 0 0 0-.349 1.82c-.036.8-.045 1.055-.045 3.092s.009 2.292.045 3.092c.013.622.13 1.238.349 1.82a3.83 3.83 0 0 0 2.194 2.195 5.5 5.5 0 0 0 1.82.348c.8.036 1.055.045 3.092.045s2.292-.009 3.092-.045a5.5 5.5 0 0 0 1.82-.349 3.84 3.84 0 0 0 2.195-2.193 5.5 5.5 0 0 0 .348-1.821c.036-.8.045-1.055.045-3.092s-.009-2.292-.045-3.092a5.5 5.5 0 0 0-.349-1.82 3.84 3.84 0 0 0-2.193-2.194 5.5 5.5 0 0 0-1.821-.349c-.8-.036-1.055-.045-3.092-.045" />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M10 10h20v20H10z" />
      </clipPath>
    </defs>
  </svg>
);
export default Instagram;
