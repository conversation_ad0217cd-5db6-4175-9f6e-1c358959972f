"use client"

import React from 'react'
import { Button } from '@/components/core/Button'
import { cn } from '@/utils/classNames'
import { shortenNumber } from '@/utils/numbers'

export default function WalletCard() {
  const [showBalance, setShowBalance] = React.useState(true);


  return (
    <div
      className={cn(
        "rounded-xl bg-[#105230] bg-[url('/images/wallet-triangle.svg')] !font-dash bg-contain bg-right bg-no-repeat p-6 text-white sm:p-3 md:p-6"
      )}
    >
      <div className="flex justify-between gap-2 md:gap-4">
        <p>
          <span className="mb-[6px] block text-sm text-white !font-dash">
            Vendboss wallet
          </span>

          <span className="text-xl font-semibold leading-normal md:text-2xl !font-dash flex items-center gap-2">
            ₦{showBalance ? shortenNumber(0.0) : '****'}
            <button
              type="button"
              onClick={() => setShowBalance((prev) => !prev)}
              className="ml-6 focus:outline-none"
              aria-label={showBalance ? "Hide balance" : "Show balance"}
            >
              {/* Simple SVG eye/eye-off icon */}
              {showBalance ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className='text-[#FFFFFFB2] h-5 w-5'><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0" /><circle cx="12" cy="12" r="3" /></svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className='text-[#FFFFFFB2] h-5 w-5'><path d="M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49" /><path d="M14.084 14.158a3 3 0 0 1-4.242-4.242" /><path d="M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143" /><path d="m2 2 20 20" /></svg>
              )}
            </button>
          </span>
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 mt-5">
        <Button
          className="flex-1 bg-white hover:bg-white/90 !font-dash text-[#105230] border-0 py-2 px-3 rounded-md text-xs font-medium transition-all duration-200"
        >
          Top-up wallet
        </Button>


        <Button
          className="flex-1 bg-white/20 hover:bg-white/30 !font-dash text-white border-0 py-2 px-3 rounded-md text-xs font-medium transition-all duration-200"
        >
          Withdraw money
        </Button>
      </div>
    </div>
  )
}
