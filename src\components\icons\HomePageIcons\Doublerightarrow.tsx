import * as React from "react";
import { SVGProps } from "react";
const DoubleRigtArrow = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={21}
    viewBox="0 0 24 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <mask
      id="a"
      style={{
        maskType: "luminance",
      }}
      maskUnits="userSpaceOnUse"
      x={4}
      y={0}
      width={20}
      height={21}
    >
      <rect
        width={20}
        height={20}
        rx={10}
        transform="matrix(-1 0 0 1 24 .5)"
        fill="#fff"
      />
    </mask>
    <g mask="url(#a)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.828 13.328a4 4 0 0 0 .151-5.497l-.15-.16-4.12-3.878a1 1 0 0 0-1.497 1.32l.083.094 4.12 3.879a2 2 0 0 1 .116 2.701l-.117.127-4.12 3.879a1 1 0 0 0 1.32 1.497l.095-.083z"
        fill="#fff"
      />
    </g>
    <mask
      id="b"
      style={{
        maskType: "luminance",
      }}
      maskUnits="userSpaceOnUse"
      x={0}
      y={0}
      width={20}
      height={21}
    >
      <rect
        width={20}
        height={20}
        rx={10}
        transform="matrix(-1 0 0 1 20 .5)"
        fill="#fff"
      />
    </mask>
    <g mask="url(#b)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.828 13.328a4 4 0 0 0 .151-5.497l-.15-.16-4.12-3.878a1 1 0 0 0-1.497 1.32l.083.094 4.12 3.879a2 2 0 0 1 .116 2.701l-.117.127-4.12 3.879a1 1 0 0 0 1.32 1.497l.095-.083z"
        fill="#fff"
        fillOpacity={0.6}
      />
    </g>
  </svg>
);
export default DoubleRigtArrow;
