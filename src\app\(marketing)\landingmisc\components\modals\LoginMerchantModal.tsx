"use client"

import React from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { But<PERSON> } from '@/components/core/Button'
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogBody,
    DialogClose,
} from '@/components/core/Dialog'
import { Input } from '@/components/core'
import { useAuth } from '@/contexts/authentication'

// Zod validation schema for login
const loginSchema = z.object({
    phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits').regex(/^\d+$/, 'Phone number must contain only digits'),
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginModalProps {
    isLoginOpen: boolean;
    setLoginModalState: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function LoginModal({ isLoginOpen, setLoginModalState }: LoginModalProps) {
    const router = useRouter()
    const { authDispatch } = useAuth()

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        reset
    } = useForm<LoginFormData>({
        resolver: zodResolver(loginSchema)
    })

    const onSubmit = async (data: LoginFormData) => {
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000))
            console.log('Login submitted:', data)

            // Simulate successful authentication with mock user data
            if (authDispatch) {
                const mockUser = {
                    message: "Login successful",
                    user_data: {
                        id: 1,
                        phone_number: data.phoneNumber,
                        first_name: "John",
                        last_name: "Doe",
                        email: "<EMAIL>",
                        wallet_balance: 0,
                        // Add other required fields with default values
                        last_login: new Date().toISOString(),
                        is_active: true,
                        date_joined: new Date().toISOString(),
                        tag: "MERCHANT",
                        username: data.phoneNumber,
                        profile_picture: "",
                        customer_id: "VDB001",
                        unique_id: "unique_001",
                        terminal_id: null,
                        terminal_serial: null,
                        terminal_provider: null,
                        date_assigned: null,
                        custom_account_provider: null,
                        type_of_user: "merchant",
                        registration_email_verified: true,
                        referral_code: null,
                        referer_code: null,
                        state: "",
                        lga: "",
                        nearest_landmark: "",
                        street: "",
                        has_login_pin: false,
                        merchant_pin: null,
                        has_transaction_pin: false,
                        has_merchant_pin: false,
                        pin_retries: 0,
                        pin_remaining_retries: 3,
                        sec_que_retries: 0,
                        sec_que_remaining_retries: 3,
                        kyc_one_image_url: null,
                        kyc_two_image_url: null,
                        kyc_level: 0,
                        kyc_one_progress: "",
                        kyc_two_progress: "",
                        kyc_three_progress: "",
                        email_subscription: true,
                        sms_subscription: true,
                        send_money_status: true,
                        block_on_funding: false,
                        bypass_duplicate_trans: false,
                        master_bvn: false,
                        bvn_number: null,
                        bvn_first_name: "",
                        bvn_last_name: "",
                        first_security_question: "",
                        second_security_question: "",
                        sales_rep_upline_code: null,
                        sales_rep_full_name: null,
                        initial_handler: null,
                        has_sales_rep: false,
                        business_name: null,
                        gender: null,
                        sales_rep_comm_balance_daily: 0,
                        sales_rep_comm_balance: 0,
                        bills_pay_comm_balance_daily: 0,
                        bills_pay_comm_balance: 0,
                        other_comm_balance_daily: 0,
                        other_comm_balance: 0,
                        firebase_key: "",
                        notify_app_token: null,
                        login_count: 1,
                        terminal_login_count: 0,
                        mobile_login_count: 1,
                        web_login_count: 0,
                        daily_terminal_login_count: 0,
                        weekly_terminal_login_count: 0,
                        monthly_terminal_login_count: 0,
                        terminal_last_login: null,
                        mobile_last_login: new Date().toISOString(),
                        web_last_login: null,
                        is_fraud: false,
                        lotto_suspended: false,
                        is_suspended: false,
                        suspension_reason: null,
                        terminal_suspended: false,
                        mobile_suspended: false,
                        terminal_disabled: false,
                        mobile_disabled: false,
                        terminal_disable_count_sum: 0,
                        mobile_disable_count_sum: 0,
                        terminal_disable_count: 0,
                        mobile_disable_count: 0,
                        role: null,
                        agent_consent: true,
                        date_of_consent: null,
                        marital_status: null,
                        vfd_bvn_acct_num_count: 0,
                        terminal_status: "active",
                        terminal_truly_active: true,
                        inactive_count: 0,
                        terminal_last_inactive: null,
                        lotto_win_toggle: false,
                        added_trans_limit: 0,
                        added_daily_trans_count: 0,
                        trans_band: 0,
                        exclude_trans_band: false,
                        change_pass_hash: null,
                        change_pass_hash_time: null,
                        ussd_active: true,
                        ussd_active_admin_lock: false,
                        num_of_other_accounts: 0
                    },
                    accounts_data: [],
                    wallets_data: []
                }
                authDispatch({ type: 'LOGIN', payload: mockUser })
            }

            // Reset form and close modal
            reset()
            setLoginModalState(false)

            // Redirect to dashboard
            router.push('/dashboard')

        } catch (error) {
            console.error('Login error:', error)
            // Handle error (show error message)
        }
    }

    const handleCancel = () => {
        reset()

    }

    return (
        <Dialog open={isLoginOpen} onOpenChange={setLoginModalState}>
            <DialogContent
                className="w-full max-w-lg mx-4 bg-white rounded-2xl border-0 p-0 max-h-[90vh] overflow-y-auto no-scrollbar"
                overlayClassName="bg-black/60 backdrop-blur-sm"
            >
                <div className="p-6">
                    <DialogHeader className="text-right bg-transparent px-0 py-0">
                        <DialogTitle className='text-lg font-medium text-black'>
                            Welcome back
                        </DialogTitle>
                        <DialogClose >
                            <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="17" cy="17" r="17" fill="#F9FAFB" />
                                <line x1="12.8201" y1="12.1123" x2="21.0141" y2="20.3063" stroke="black" />
                                <line y1="-0.5" x2="11.5881" y2="-0.5" transform="matrix(-0.707107 0.707107 0.707107 0.707107 21.2463 12.4658)" stroke="black" />
                            </svg>
                        </DialogClose>
                    </DialogHeader>

                    <DialogBody className="px-0 py-0">
                        {/* Form */}
                        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-6">
                            {/* Phone Number */}
                            <div>
                                <label htmlFor="phoneNumber" className="block text-[13px] font-medium text-black mb-1">
                                    Phone Number
                                </label>
                                <Input
                                    {...register('phoneNumber')}
                                    type="tel"
                                    id="phoneNumber"
                                    placeholder="Enter phone number"
                                    className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                                />
                                {errors.phoneNumber && (
                                    <p className="text-red-500 text-[10px] mt-1">{errors.phoneNumber.message}</p>
                                )}
                            </div>
                            {/* Buttons */}
                            <div className='mt-10 w-full h-[1px] bg-[#E9EBEE]'></div>
                            <div className="flex space-x-3 pt-4">
                                <Button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="bg-[#105230] hover:bg-green-700 text-white py-3 px-9 rounded-full font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {isSubmitting ? 'Processing...' : 'Login'}
                                </Button>
                                <Button
                                    type="button"
                                    onClick={handleCancel}
                                    className=" bg-[#E7EEEA] hover:bg-gray-300 text-[#105230] py-3 px-9 rounded-full font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Button>
                            </div>
                        </form>
                    </DialogBody>
                </div>
            </DialogContent>
        </Dialog>
    )
}
