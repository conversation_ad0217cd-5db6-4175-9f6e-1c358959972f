import './globals.css';

import { <PERSON><PERSON><PERSON>, D<PERSON>_Sans, <PERSON><PERSON>, <PERSON><PERSON>_Madefor_Display, Inter, Roboto } from 'next/font/google';
//import localFont from 'next/font/local';,
// import { Providers } from '@/providers';

import { cn } from '@/utils/classNames';
import { ReactQueryProvider } from '@/lib/reactQuery';


import { AuthProvider } from '@/contexts/authentication';
// import ProtectedRouteGuard from './(auth)/(onboarding)/misc/components/ProtectedRouteGuard';
import { Wrapper } from './(auth)/(onboarding)/misc/components/Wrapper';
// import { Wrapper } from './(auth)/(onboarding)/misc/components/Wrapper';
// import ProtectedRouteGuard from './(auth)/(onboarding)/misc/components/ProtectedRouteGuard';


const fontSans = Manrope({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Manrope',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontDash = DM_Sans({
  subsets: ['latin'],
  variable: '--font-dash',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'DM_Sans',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});


const fontHeading = Sora({
  subsets: ['latin'],
  variable: '--font-heading',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Sora',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});

const fontWixDisplay = Wix_Madefor_Display({
  subsets: ['latin'],
  variable: '--font-wix-display',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});


const fontRoboto = Roboto({
  subsets: ['latin'],
  variable: '--font-roboto',
  weight: ['400', '500', '700'], // adjust as needed
  display: 'swap',
});

const fontInter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

// const fontClash = localFont({
//   src: './fonts/ClashDisplay-Variable.woff2',
//   variable: '--font-clash',
//   display: 'swap',
// });

export const metadata = {
  title: 'Vendboss',
  description:
    'Liberty vendboss',
  // verification: {
  //   google: 'LVQp31oO5gKpCoGlSMnsa6o-5xsVvQ9fn4WCy85OMGI',
  // },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>

        <link href="/favicon.ico" rel="icon" sizes="any" />
      </head>
      <body
        suppressHydrationWarning
        className={cn(
          'min-h-screen h-full  overflow-auto font-sans antialiased',
          fontSans.variable,
          fontDash.variable,
          fontHeading.variable,
          fontWixDisplay.variable,
          //fontClash.variable
          fontInter.variable,
          fontRoboto.variable
        )}
      >


        {/* <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 99999,
            },
          }}
        /> */}

        {/* <Providers>{children}</Providers> */}

        <ReactQueryProvider>
          <AuthProvider>
            {/* <ProtectedRouteGuard> */}

            <Wrapper>
              {children}
            </Wrapper>

            {/* </ProtectedRouteGuard> */}
          </AuthProvider>
        </ReactQueryProvider>








      </body>
    </html>
  );
}
