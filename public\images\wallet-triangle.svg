<svg width="124" height="141" viewBox="0 0 124 141" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.1" filter="url(#filter0_d_1_2)">
<rect width="52.4799" height="135.998" rx="2.05997" transform="matrix(0.836954 -0.547273 -0.667681 -0.744448 137.803 97.9639)" fill="url(#paint0_linear_1_2)" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter1_d_1_2)">
<rect width="50.8956" height="272.066" rx="2.05997" transform="matrix(0.965926 -0.258819 -0.402079 -0.915605 113.392 185.277)" fill="url(#paint1_linear_1_2)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_1_2" x="43.7741" y="-31.2432" width="141.178" height="136.689" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.11994"/>
<feGaussianBlur stdDeviation="2.05997"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_2" result="shape"/>
</filter>
<filter id="filter1_d_1_2" x="0.542169" y="-76.541" width="165.469" height="269.599" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.11994"/>
<feGaussianBlur stdDeviation="2.05997"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_2" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_2" x1="26.2399" y1="0" x2="26.2399" y2="135.998" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1_2" x1="25.4478" y1="0" x2="25.4478" y2="272.066" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
