@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);


  --font-sans: var(--font-sans);
  --font-dash: var(--font-dash);
  --font-heading: var(--font-heading);
  --font-wix-display: var(--font-wix-display);
  --font-inter: var(--font-inter);
  --font-roboto: var(--font-roboto);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family:  var(--font-sans), ui-sans-serif, system-ui;
}


p,
span,
div,
button,
input,
textarea,
select {
  font-family: var(--font-sans), ui-sans-serif, system-ui;
}


/* .no-scrollbar::-webkit-scrollbar {
  width: 5px
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: 5px;
} */

.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, and Opera */
}

.reactEasyCrop_CropAreaGrid {
  height: 100px !important;
  width: 100% !important;
}

/* Hide scrollbar on WebKit browsers */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar on Firefox */
.scrollbar-hide {
  scrollbar-width: none;
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-50%);
  }
}

/* Added slower marquee animation for lifestyle images */
@keyframes marquee-slow {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 20s linear infinite;
}

/* Added slower marquee class for footer gallery */
.animate-marquee-slow {
  animation: marquee-slow 40s linear infinite;
}