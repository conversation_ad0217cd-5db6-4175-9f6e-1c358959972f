

import axios from 'axios';

import { useQuery } from '@tanstack/react-query';
import { User } from '../types/users';

// import { UserEntities } from '../types';

export interface NoPinError {
  error: string;
  message: string;
  create_transaction_pin_link: string;
}

export const getUserDetails = async () => {
  const response = await axios.get(`/agency/user/get_user_details/`);
  return response.data as User;
};

export const useUserDetails = ({
  retryCount,
  initialData,
}: {
  retryCount?: number | boolean;
  initialData?: User;
}) => {
  return useQuery({
    queryKey: ['user-details'],
    queryFn: getUserDetails,
    retry: retryCount ?? true,
    initialData,
    staleTime: 0, // Ensures data is always refetched when invalidated
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: true, // Allows refetching on window focus
    refetchOnMount: true, // Ensures fresh data on mount
  });
};

// export const getUserDetails = async (): Promise<UserEntities> => {
//   const { data } = await authAxios.get('/agency/user/get_user_details/');
//   return data;
// };

// export const useUser = ({}) =>
//   useQuery('user-details', getAuthenticatedUser, { cacheTime: 1000 * 60 * 5 });x
