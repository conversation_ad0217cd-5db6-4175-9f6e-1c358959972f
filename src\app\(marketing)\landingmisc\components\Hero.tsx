'use client';

import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';
import Image from 'next/image';
import { Button } from '@/components/core/Button';
import { MarketingHeader } from '@/components/layout/marketing';
import SmallHome from '@/components/icons/HomePageIcons/SmallHome';
import DoubleRigtArrow from '@/components/icons/HomePageIcons/Doublerightarrow';
import SmallLocation from '@/components/icons/HomePageIcons/SmallLocation';
import Property from '@/components/icons/HomePageIcons/Property';
import Powered from '@/components/icons/HomePageIcons/Powered';

export function Hero() {
  return (
    <section className="relative overflow-visible bg-[#000619]">
      <MarketingHeader />

      <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 w-full md:gap-20">
        <div className="flex flex-col justify-end px-6 lg:px-24">
          <div className='rounded-[0.5rem] flex items-center justify-center bg-[#081934] bg-opacity-[20%] max-w-[60%] py-3 text-sm md:text-base gap-2'>
            <SmallHome />
            <p className='text-[#fff] font-medium text-[1rem]'>Rent Now, Pay Later</p>
          </div>

          <h1 className="mt-4 text-[#fff] text-[1.625rem] sm:text-[2.8rem] md:text-[2rem] lg:text-[2.5rem] font-extrabold">
            <Balancer>
              Finding a Home Is Tough, Paying Rent Shouldn’t Be.
            </Balancer>
          </h1>

          <p className="text-[#fff] text-[1rem] max-w-[42rem] mt-4 font-normal">
            <Balancer>
              We exist to make rent simple, flexible, and stress-free. By covering your
              upfront rent, we let you move in now and pay later with zero stress.
            </Balancer>
          </p>

          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mt-10 mb-12 w-full sm:w-auto">
            <Button className='bg-[#2E6CE7] text-[#FFFFFF] font-medium text-[1rem]'>
              <div className="flex gap-4">
                Apply Now
                <DoubleRigtArrow />
              </div>
            </Button>

            <Button className='text-[#FFFFFF] font-medium text-[1rem] border border-[#fff]'>
              <div className="flex gap-4">
                Talk to Us
                <DoubleRigtArrow />
              </div>
            </Button>
          </div>

          <div className='rounded-[0.5rem] bg-[#081934] bg-opacity-[20%] w-full px-10 py-6'>
            <div className="flex gap-6">
              <div>
                <div className="flex gap-3">
                  <SmallLocation />
                  <p className='text-[#fff] font-medium text-sm'>Location</p>
                </div>
                <p className='text-white font-medium text-[1rem] mt-2'>All Locations</p>
              </div>

              <div className="h-[52px] w-[1px] border-l border-[#ffffff]" />

              <div>
                <div className="flex gap-3">
                  <Property />
                  <p className='text-[#fff] font-medium text-sm'>Property Type</p>
                </div>
                <p className='text-white font-medium text-[1rem] mt-2'>Residential & Commercial</p>
              </div>

              <div className="h-[52px] w-[1px] border-l border-[#ffffff]" />

              <div>
                <div className="flex gap-3">
                  <Powered />
                  <p className='text-[#fff] font-medium text-sm'>Powered By</p>
                </div>
                <p className='text-white font-medium text-[1rem] mt-2'>Seeds & Pennies</p>
              </div>
            </div>
          </div>
        </div>


        <div className="relative justify-self-end min-w-[300px] w-[70%] max-w-[550px]  p-3  !h-[120%] max-h-[800px] bg-[#000619] rounded-xl">
          <div className="relative  w-full  !h-full   ">
            <Image
              src="/images/HomeCouple.png"
              alt="Happy Couple"
              // width={800}
              // height={690}
              fill
              objectFit='cover'
              className='rounded-2xl z-[3]'
            />
          <div className='absolute -left-[10%] w-[50px] h-[50px] bg-[#000619] bottom-0 z-[1] ' />
          <div className='absolute -left-[10%] w-[50px] h-[50px] bg-[#e0e7fc] bottom-0 z-[3]' />
        </div>
          </div>  
      </div>
    </section>
  );
}


