import React from 'react'
import Balancer from 'react-wrap-balancer'
import Plan from './Plan'

export default function GetStarted() {
    return (
        <div className="-mt-20 bg-[#E0E7FC] rounded-t-[10rem] overflow-hidden py-[6rem] px-[6.25rem]">
      <div className='rounded-[0.5rem] flex items-center justify-center bg-[#D4DFFF] max-w-[20%] py-3 text-sm md:text-base gap-2'>
        <p className='text-[#000000] font-semibold text-[1rem]'>What You Need to Get Started</p>
      </div>

      <p className="text-[#000619CC]/80 text-[1rem] max-w-[35rem] mt-4 font-semibold">
        <Balancer>
          We’ve made it simple. Just a few documents and details,
          and you’re on your way to flexible rent payments with
          instant move-in access.
        </Balancer>
      </p>

      <div className="mt-[2.625rem]">
      <Plan/>
      </div>
      
    </div>
    )
}
