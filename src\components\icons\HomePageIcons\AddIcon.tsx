import * as React from "react";
import { SVGProps } from "react";
const AddIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={27}
    height={27}
    viewBox="0 0 27 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={26.028} height={26.028} rx={5.645} fill="#262C40" />
    <rect
      x={0.314}
      y={0.314}
      width={25.401}
      height={25.401}
      rx={5.331}
      stroke="#fff"
      strokeOpacity={0.35}
      strokeWidth={0.627}
    />
    <path d="M13.725 13.723h3.826v-1.275H8.623v1.275z" fill="#fff" />
    <path d="M12.456 13.723v3.826h1.275V8.621h-1.275z" fill="#fff" />
  </svg>
);
export default AddIcon;
