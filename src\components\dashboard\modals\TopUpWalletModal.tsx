"use client"

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogBody
} from '@/components/core/Dialog'
import { Button } from '@/components/core/Button'
import { Input } from '@/components/core/Input'

interface TopUpWalletModalProps {
  isOpen: boolean
  onClose: () => void
}

type PaymentMethod = 'bank' | 'card'

export default function TopUpWalletModal({ isOpen, onClose }: TopUpWalletModalProps) {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('card')
  const [step, setStep] = useState<'method' | 'details'>('method')

  const handleMethodSelect = (method: PaymentMethod) => {
    setPaymentMethod(method)
    setStep('details')
  }

  const handleBack = () => {
    setStep('method')
  }

  const handleClose = () => {
    setStep('method')
    setPaymentMethod('card')
    onClose()
  }

  const handleProceed = () => {
    // Handle payment processing logic here
    console.log('Processing payment with method:', paymentMethod)
    handleClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="w-full max-w-md mx-4 bg-white rounded-2xl border-0 p-0"
        overlayClassName="bg-black/60 backdrop-blur-sm"
      >
        <div className="p-6">
          <DialogHeader className="flex flex-row items-center justify-between bg-transparent px-0 py-0 mb-4">
            <DialogTitle className="text-lg font-medium text-black">
              Wallet Top-Up
            </DialogTitle>
            <DialogClose>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </DialogClose>
          </DialogHeader>

          <DialogBody className="px-0 py-0">
            {step === 'method' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 mb-6">
                  How would you like to fund your wallet?
                </p>

                {/* Payment Method Options */}
                <div className="space-y-3">
                  {/* Bank Transfer Option */}
                  <div
                    className={`flex items-center justify-between text-black p-4 border rounded-lg cursor-pointer transition-colors ${paymentMethod === 'bank' ? 'border-[#********] bg-green-50' : 'border-gray-200 hover:border-gray-300'
                      }`}
                    onClick={() => setPaymentMethod('bank')}
                  >
                    <span className="text-sm font-medium">Bank Transfer</span>
                    <div className={`w-4 h-4 rounded-full border-2 ${paymentMethod === 'bank' ? 'border-[#105230] bg' : 'bg-white border-[#E9EBEE]'
                      }`}>
                      {paymentMethod === 'bank' && (
                        <div className="w-full h-full rounded-full bg-[#105230] scale-50"></div>
                      )}
                    </div>
                  </div>

                  {/* Card Payment Option */}
                  <div
                    className={`flex items-center justify-between text-black p-4 border rounded-lg cursor-pointer transition-colors ${paymentMethod === 'card' ? 'border-[#********] bg-green-50' : 'border-gray-200 hover:border-gray-300'
                      }`}
                    onClick={() => setPaymentMethod('card')}
                  >
                    <span className="text-sm font-medium">Card Payment</span>
                    <div className={`w-4 h-4 rounded-full border-2 ${paymentMethod === 'card' ? 'border-[#105230] bg-white' : 'bg-white border-[#E9EBEE]'
                      }`}>
                      {paymentMethod === 'card' && (
                        <div className="w-full h-full rounded-full bg-[#105230] scale-50"></div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Proceed Button */}
                <Button
                  onClick={() => handleMethodSelect(paymentMethod)}
                  className="w-full bg-[#105230] hover:bg-[#0d4228] text-white py-3 rounded-lg font-medium mt-6"
                >
                  Proceed to payment
                </Button>
              </div>
            )}

            {step === 'details' && paymentMethod === 'bank' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 mb-4">
                  Transfer to the account below to fund your wallet account
                </p>

                {/* Account Details */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Account Number
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        value="*************"
                        readOnly
                        className="flex-1 bg-gray-50 border-gray-200"
                      />
                      <Button
                        className="px-3 py-2 text-xs"
                        onClick={() => navigator.clipboard.writeText('*************')}
                      >
                        Copy
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Account Name
                    </label>
                    <Input
                      value="{Vendboss/ {Logged in user}}"
                      readOnly
                      className="bg-gray-50 border-gray-200"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Bank
                    </label>
                    <Input
                      value="LibertyPay MFB"
                      readOnly
                      className="bg-gray-50 border-gray-200"
                    />
                  </div>
                </div>

                {/* Confirm Button */}
                <Button
                  onClick={handleProceed}
                  className="w-full bg-[#105230] hover:bg-[#0d4228] text-white py-3 rounded-lg font-medium mt-6"
                >
                  Confirm Transfer
                </Button>
              </div>
            )}

            {step === 'details' && paymentMethod === 'card' && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 mb-4">
                  Enter your card details to proceed with payment
                </p>

                {/* Card Payment Form */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Amount
                    </label>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Card Number
                    </label>
                    <Input
                      type="text"
                      placeholder="1234 5678 9012 3456"
                      className="w-full"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Expiry Date
                      </label>
                      <Input
                        type="text"
                        placeholder="MM/YY"
                        className="w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        CVV
                      </label>
                      <Input
                        type="text"
                        placeholder="123"
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>

                {/* Proceed Button */}
                <Button
                  onClick={handleProceed}
                  className="w-full bg-[#105230] hover:bg-[#0d4228] text-white py-3 rounded-lg font-medium mt-6"
                >
                  Proceed to payment
                </Button>
              </div>
            )}

            {/* Back button for details step */}
            {step === 'details' && (
              <Button
                onClick={handleBack}
                className="w-full mt-3 border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Back
              </Button>
            )}
          </DialogBody>
        </div>
      </DialogContent>
    </Dialog>
  )
}
