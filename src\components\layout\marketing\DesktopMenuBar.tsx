'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import { cn } from '@/utils/classNames';

interface DesktopMenuLinkProps {
  link: string;
  text: string;
  disabled: boolean;
  isExternal: boolean;
}

export function DesktopMenuLink({
  text,
  link,
  disabled,
  isExternal,
}: DesktopMenuLinkProps) {
  const pathname = usePathname();
  const isSelected = pathname === link;

//    const getTextStyles = () => {
//   const purpleLinks = ['Home', 'Services', 'About us'];
//   const baseFontWeight = isSelected ? 'font-bold' : 'font-normal';

//   // if (text === 'Contact us') {
    
//   //   if (pathname === '/') {
//   //     return cn('text-[#F9F9F9]', baseFontWeight);
//   //   } else {
//   //     return cn('text-[#4E2667]', baseFontWeight, 'text-[1rem] font-sans');
//   //   }
//   // }

//   // if (purpleLinks.includes(text)) {
//   //   return cn('text-[#4E2667]', baseFontWeight, 'text-[1rem] font-sans');
//   // }

//   // return cn('text-black', baseFontWeight);
// };


  if (isExternal) {
    return (
      <a
        className={cn(
          'inline-block  text-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5  xl:text-base ',
          // getTextStyles()
            isSelected && 'hidden'
        )}
        href={link}
        rel="noopener noreferrer"
        target="_blank"
      >
        {text}
      </a>
    );
  }

  if (disabled) {
    return (
      <button
        className={cn(
          'inline-block cursor-not-allowed bg-[#fafafa]  text-sm opacity-50  xl:text-base ',
          // getTextStyles()
           isSelected && 'hidden'
        )}
        disabled
      >
        {text}
      </button>
    );
  }

  return (
    <Link
      className={cn(
        'inline-block  text-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5 md:px-[1rem] lg:px-[2.125rem] py-[0.625rem] xl:text-base',
        // getTextStyles()
      )}
      href={link}
    >
      {text}
    </Link>
  );
}

export const linkGroups = [
  {
    link: '/',
    text: 'Home',
    icon: undefined,
    disabled: false,
    isExternal: false,
  },



  {
    link: '/marketingsections/about-us',
    text: 'About us',
    icon: undefined,
    disabled: false,
    isExternal: false,

  },

   {
    link: '/marketingsections/faq',
    text: 'FAQs',
    icon: undefined,
    disabled: false,
    isExternal: false,

  },

  {
    link: '/marketingsections/contact-us',
    text: 'Contact us',
    icon: undefined,
    disabled: false,
    isExternal: false,
  },

];

interface DesktopMenuBarProps {
  isColored: boolean;
}

export function DesktopMenuBar({ isColored }: DesktopMenuBarProps) {
  return (
    <nav className="hidden md:block">
      <ul
        className={cn(
          'flex items-center   rounded-[0.5rem]   bg-[#D8E7FF] transition-all duration-300 ease-in-out  text-[#ffff]',
          isColored && 'bg-[#FFFFFF0D]/50'
        )}
      >
        {linkGroups.map(({ link, text, disabled, isExternal }) => {
          return (
            <li key={link}>
              <DesktopMenuLink
                disabled={disabled}
                isExternal={isExternal}
                link={link}
                text={text}
              />
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
