"use client"

import React, { useState } from 'react'
import { Button } from '@/components/core/Button'

export default function ReferralSection() {
  const [copied, setCopied] = useState(false)
  const referralLink = "https://www.vendboss.com/{user}"
  const totalReferrals = 483

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  return (
    <div className="bg-white rounded-xl p-4 border border-gray-100">
      {/* Header with total referrals */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 !font-dash">Your referral link</h3>
        <div className="flex  items-center gap-1">
          <div className="text-[10px] text-black">Total Referrals: </div>
          <div className="text-[10px] font-bold text-black">{totalReferrals}</div>
        </div>
      </div>

      <p className="text-xs text-black font-light mb-3 !font-dash">
        Share your link, and get paid everytime your friends pay their bills on Vendboss
      </p>

      {/* Referral Link Input */}
      <div className="flex items-center bg-[#E7EEEA] rounded-lg p-2">
        <input
          type="text"
          value={referralLink}
          readOnly
          className="flex-1 bg-transparent text-xs text-[#105230] outline-none"
        />
        <Button
          onClick={handleCopyLink}
          className="ml-2 bg-[#105230] hover:bg-green-700 text-white px-4 py-2 rounded-md text-xs font-medium transition-colors duration-200"
        >
          {copied ? 'Copied!' : 'Copy'}
        </Button>
      </div>
    </div>
  )
}
