import * as React from "react";
import { SVGProps } from "react";
const SmallHome = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={24} height={24} rx={4} fill="#0C254D" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.8 12h1.111v5.6a.4.4 0 0 0 .4.4h10.577a.4.4 0 0 0 .4-.4V12H18.4a.4.4 0 0 0 .264-.7l-6.8-6a.4.4 0 0 0-.528 0l-6.8 6a.4.4 0 0 0 .264.7m1.511-.8h-.453L11.6 6.134l5.743 5.066h-.454a.4.4 0 0 0-.4.4v5.6H6.712v-5.6a.4.4 0 0 0-.4-.4"
      fill="#fff"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.567 13.2h-2a.8.8 0 0 0-.8.8v3.2a.8.8 0 0 0 .8.8h2a.8.8 0 0 0 .8-.8V14a.8.8 0 0 0-.8-.8m-2 4V14h2v3.2z"
      fill="#fff"
    />
  </svg>
);
export default SmallHome;
