"use client"

import { But<PERSON> } from '@/components/core/Button';
import { Input } from '@/components/core/Input';
import * as React from 'react';

export default function VendHero() {
  const [phoneNumber, setPhoneNumber] = React.useState('')
  const [isVisible, setIsVisible] = React.useState(false)

  React.useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <section className="relative min-h-screen bg-[#105230] overflow-hidden">      {/* Background decorative elements */}

      {/* Header Navigation */}
      <div className="relative z-10 container mx-auto px-6 py-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <h1 className="text-white text-xl font-bold">VENDBOSS</h1>
          </div>
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-white hover:text-yellow-400 transition-colors">Merchants</a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors">FAQ</a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors">Contact</a>
          </nav>
        </div>
      </div>

      {/* Main content area - Two column layout */}
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[70vh]">

          {/* Left Column - Content */}
          <div className={`space-y-8 transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold text-white leading-tight">
                Make payment,<br />
                <span className="text-yellow-400">earn money.</span>
              </h1>
              <p className="text-white/80 text-lg max-w-md">
                A smarter way to make payment and earn money while you sleep - all in one place
              </p>
            </div>

            {/* Phone Input */}
            <div className="space-y-4">
              <div className="flex bg-white rounded-full p-2 max-w-md">
                <Input
                  type="tel"
                  placeholder="Your phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="border-0 bg-transparent flex-1 focus-visible:ring-0 text-gray-800 placeholder:text-gray-500"
                />
                <Button className="bg-[#105230] hover:bg-[#0d4428] text-white px-8 rounded-full">
                  Proceed
                </Button>
              </div>
              <p className="text-white/60 text-sm">
                By continuing, you agree to our Terms and Conditions
              </p>
            </div>
          </div>

          {/* Right Column - Hero Image */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative">
              {/* White background container for the hero image */}
              <div className="">
                {/* Hero Image */}
                <div className="w-[300px] h-[400px] lg:w-[536px] lg:h-[649.28px] overflow-hidden">
                  <img
                    src="/images/hero-man.png"
                    alt="Hero Image"
                    className="w-full h-full object-cover"
                  />
                </div>

              </div>


            </div>
          </div>
        </div>
      </div>
    </section>
  );
}