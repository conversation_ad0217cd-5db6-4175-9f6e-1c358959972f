'use client';

import * as React from 'react';
import Image from 'next/image';

export default function VendHero() {
  return (
    <section className="relative min-h-screen bg-[#105230] overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Decorative circles */}
        <div className="absolute top-16 left-1/3 w-3 h-3 bg-yellow-400 rounded-full"></div>
        <div className="absolute top-12 right-1/4 w-4 h-4 bg-yellow-400 rounded-full"></div>
        <div className="absolute bottom-32 right-20 w-3 h-3 bg-yellow-400 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-2 h-2 bg-yellow-400 rounded-full"></div>

        {/* Outlined circles */}
        <div className="absolute top-32 right-1/3 w-12 h-12 border-2 border-yellow-400/60 rounded-full"></div>
        <div className="absolute bottom-1/3 left-1/4 w-8 h-8 border-2 border-yellow-400/50 rounded-full"></div>
        <div className="absolute top-1/2 left-1/3 w-6 h-6 border border-yellow-400/40 rounded-full"></div>

        {/* Naira coins positioned like in target design */}
        <div className="absolute top-32 right-1/4 transform rotate-12">
          <div className="w-12 h-12 bg-gradient-to-br from-orange-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg border-2 border-orange-300/50">
            <span className="text-white font-bold text-lg drop-shadow-sm">₦</span>
          </div>
        </div>

        <div className="absolute bottom-16 left-12 transform -rotate-6">
          <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 via-orange-400 to-orange-500 rounded-full flex items-center justify-center border-4 border-yellow-300/60 shadow-xl">
            <span className="text-white font-bold text-xl drop-shadow-md">₦</span>
          </div>
        </div>

        <div className="absolute bottom-12 right-16 transform rotate-45">
          <div className="w-14 h-14 bg-gradient-to-br from-orange-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg border-2 border-orange-300/60">
            <span className="text-white font-bold text-lg drop-shadow-sm">₦</span>
          </div>
        </div>
      </div>

      {/* Header Navigation */}
      <div className="relative z-10 container mx-auto px-6 py-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <h1 className="text-white text-xl font-bold">VENDBOSS</h1>
          </div>
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-white hover:text-yellow-400 transition-colors">Merchants</a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors">FAQ</a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors">Contact</a>
          </nav>
        </div>
      </div>

      {/* Main content area - Two column layout */}
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[70vh]">

          {/* Left Column - Content */}
          <div className="relative space-y-8">
            <div>
              <h1 className="text-white text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight">
                Make payment,<br />
                <span className="text-white">earn money.</span>
              </h1>
              <p className="text-white/80 text-lg mt-6 max-w-md leading-relaxed">
                A smarter way to make payment and earn money while you sleep - all in one place.
              </p>
            </div>

            {/* Phone input section */}
            <div className="flex items-center space-x-3 max-w-md">
              <input
                type="tel"
                placeholder="Your phone number"
                className="flex-1 bg-white/20 backdrop-blur-md border border-white/30 rounded-xl px-4 py-3 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              />
              <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 hover:scale-105">
                Proceed
              </button>
            </div>

            <p className="text-white/60 text-sm">
              No account? No problem! Get started in seconds.
            </p>

            {/* Large Naira coins at bottom left */}
            <div className="absolute -bottom-16 -left-8 flex space-x-4">
              <div className="w-20 h-20 bg-yellow-400/30 rounded-full flex items-center justify-center backdrop-blur-sm border border-yellow-400/40">
                <span className="text-yellow-400 font-bold text-2xl">₦</span>
              </div>
              <div className="w-16 h-16 bg-yellow-400/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-yellow-400/30 mt-4">
                <span className="text-yellow-400 font-bold text-xl">₦</span>
              </div>
            </div>
          </div>

          {/* Right Column - Hero Image with floating UI elements */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative">
              {/* Hero Image */}
              <div className="w-[300px] h-[400px] lg:w-[350px] lg:h-[450px] bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-2xl flex items-center justify-center shadow-2xl">
                <div className="text-center text-white">
                  <div className="text-6xl mb-4">🎉</div>
                  <p className="text-lg font-bold">Hero Image</p>
                  <p className="text-sm opacity-80">Place hero-man.png in public/images/</p>
                </div>
              </div>

              {/* Floating UI Elements around the image */}

              {/* Success notification - top left of image */}
              <div className="absolute -top-8 -left-16 bg-white/98 backdrop-blur-md rounded-2xl p-4 max-w-xs shadow-2xl border border-green-100 transform -rotate-2">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-700">Your withdrawal of <span className="font-bold text-green-600">₦52,983.17</span> has</p>
                    <p className="text-xs text-gray-700">been successfully completed.</p>
                  </div>
                </div>
              </div>

              {/* Combined Commission and DSTV notification - bottom of image */}
              <div className="absolute -bottom-12 -left-8 bg-white/98 backdrop-blur-md rounded-2xl p-4 max-w-md shadow-2xl border border-gray-100 transform rotate-1">
                <p className="text-xs text-gray-500 mb-3">Just now | 2mins ago</p>
                <p className="text-sm text-gray-700 mb-4">
                  <span className="font-bold text-green-600">₦52,983.17</span> commission has been paid to your wallet. Proceed to withdraw
                </p>

                {/* DSTV Package section within the same card */}
                <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xs">DSTV</span>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800 text-xs">DSTV Family Package</p>
                    </div>
                  </div>
                  <p className="text-lg font-bold text-gray-900">₦7,800</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section >
  );
}
