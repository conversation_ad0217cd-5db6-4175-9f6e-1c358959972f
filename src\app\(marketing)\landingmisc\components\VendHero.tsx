'use client';

import * as React from 'react';
import Image from 'next/image';

export function VendHero() {
  return (
    <section className="relative min-h-screen bg-[#105230] overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Shadow strips - more subtle and varied */}
        <div className="absolute top-20 left-10 w-40 h-0.5 bg-gradient-to-r from-white/20 to-transparent rotate-12"></div>
        <div className="absolute top-40 right-20 w-32 h-0.5 bg-gradient-to-l from-white/20 to-transparent -rotate-12"></div>
        <div className="absolute bottom-40 left-20 w-36 h-0.5 bg-gradient-to-r from-white/15 to-transparent rotate-45"></div>
        <div className="absolute top-60 left-1/2 w-28 h-0.5 bg-gradient-to-r from-white/10 to-transparent -rotate-6"></div>

        {/* Decorative circles - more varied sizes and positions */}
        <div className="absolute top-16 left-1/3 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
        <div className="absolute top-32 right-1/4 w-2 h-2 bg-yellow-300 rounded-full"></div>
        <div className="absolute bottom-32 right-16 w-4 h-4 bg-yellow-500 rounded-full"></div>
        <div className="absolute top-3/4 left-1/4 w-2.5 h-2.5 bg-yellow-400 rounded-full"></div>

        {/* Outlined circles - more dynamic */}
        <div className="absolute top-48 left-1/4 w-8 h-8 border-2 border-yellow-400/60 rounded-full"></div>
        <div className="absolute bottom-48 left-1/3 w-6 h-6 border-2 border-yellow-300/50 rounded-full"></div>
        <div className="absolute top-1/3 right-1/3 w-10 h-10 border border-yellow-400/40 rounded-full"></div>

        {/* Naira coins - enhanced with better gradients and positioning */}
        <div className="absolute top-24 right-1/3 transform rotate-12">
          <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg border-2 border-yellow-300/50">
            <span className="text-white font-bold text-lg drop-shadow-sm">₦</span>
          </div>
        </div>

        <div className="absolute bottom-20 left-16 transform -rotate-6">
          <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 via-orange-400 to-orange-500 rounded-full flex items-center justify-center border-4 border-yellow-300/60 shadow-xl">
            <span className="text-white font-bold text-xl drop-shadow-md">₦</span>
          </div>
        </div>

        <div className="absolute top-1/2 left-8 transform rotate-45">
          <div className="w-10 h-10 bg-gradient-to-br from-yellow-300 via-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-md border border-yellow-200/50">
            <span className="text-white font-bold drop-shadow-sm">₦</span>
          </div>
        </div>

        <div className="absolute bottom-1/3 right-12 transform -rotate-12">
          <div className="w-14 h-14 bg-gradient-to-br from-orange-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center border-2 border-orange-300/60 shadow-lg">
            <span className="text-white font-bold text-lg drop-shadow-sm">₦</span>
          </div>
        </div>

        {/* Additional smaller coins for more richness */}
        <div className="absolute top-1/4 left-1/2 transform rotate-30">
          <div className="w-8 h-8 bg-gradient-to-br from-yellow-300 to-yellow-400 rounded-full flex items-center justify-center shadow-md">
            <span className="text-white font-semibold text-sm">₦</span>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-12">
          <h1 className="text-yellow-400 text-2xl font-bold">VEND</h1>
        </div>

        {/* Main content area */}
        <div className="relative min-h-[80vh] flex items-center justify-center">
          {/* Floating UI Elements positioned absolutely */}

          {/* Phone number input - top left */}
          <div className="absolute top-20 left-12 flex items-center space-x-3 transform -rotate-2">
            <div className="bg-white/25 backdrop-blur-md rounded-xl px-5 py-3 text-white border border-white/40 shadow-lg">
              <span className="text-sm font-medium">Your phone number</span>
            </div>
            <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-xl transition-all duration-200 hover:scale-105">
              Proceed
            </button>
          </div>

          {/* Success notification - center left */}
          <div className="absolute top-1/3 left-16 bg-white/98 backdrop-blur-md rounded-2xl p-5 max-w-sm shadow-2xl border border-green-100 transform rotate-1">
            <div className="flex items-start space-x-4">
              <div className="w-7 h-7 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <p className="text-sm text-gray-700 leading-relaxed">Your withdrawal of <span className="font-bold text-green-600">₦52,983.17</span> has</p>
                <p className="text-sm text-gray-700 leading-relaxed">been successfully completed.</p>
              </div>
            </div>
          </div>

          {/* Commission notification - bottom left */}
          <div className="absolute bottom-36 left-20 bg-white/98 backdrop-blur-md rounded-2xl p-5 max-w-md shadow-2xl border border-gray-100 transform -rotate-1">
            <p className="text-xs text-gray-500 mb-3 font-medium">Just now | 2mins ago</p>
            <p className="text-sm text-gray-700 leading-relaxed">
              <span className="font-bold text-green-600 text-base">₦52,983.17</span> commission has been paid to your wallet. Proceed to withdraw
            </p>
          </div>

          {/* DSTV Package - bottom right */}
          <div className="absolute bottom-24 right-36 bg-white/98 backdrop-blur-md rounded-2xl p-5 shadow-2xl border border-blue-100 transform rotate-2">
            <div className="flex items-center space-x-4">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-xs">DSTV</span>
              </div>
              <div>
                <p className="font-semibold text-gray-800 text-sm">DSTV Family Package</p>
                <p className="text-xl font-bold text-gray-900">₦7,800</p>
              </div>
            </div>
          </div>

          {/* Central Hero Image */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              {/* Placeholder for hero image - replace with actual image */}
              <div className="w-[350px] h-[450px] bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-2xl flex items-center justify-center shadow-2xl">
                <div className="text-center text-white">
                  <div className="text-6xl mb-4">🎉</div>
                  <p className="text-lg font-bold">Hero Image</p>
                  <p className="text-sm opacity-80">Place hero-man.png in public/images/</p>
                </div>
              </div>
              {/* Uncomment when image is available:
              <Image
                src="/images/hero-man.png"
                alt="Happy man in yellow shirt celebrating with phone"
                width={350}
                height={450}
                className="object-contain rounded-2xl shadow-2xl"
                priority
              />
              */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
