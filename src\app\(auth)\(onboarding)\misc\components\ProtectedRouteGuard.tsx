'use client';

import { usePathname, useRouter } from 'next/navigation';
import * as React from 'react';

import { useAuth } from '@/contexts/authentication';
import Spinner from '@/components/core/Spinner';


interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRouteGuard({ children }: ProtectedRouteProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useAuth();
  const { isAuthenticated, isLoading } = authState;

  const protectedRoutes = [
    '/dashboard',
  ];

  const _publicRoutes = [
    '/',
    '/login',
    '/sign-up',
    '/about',
    '/contact',
  ];

  const isPublicPath = React.useMemo(() => {
    return _publicRoutes.some(route => pathname === route || pathname.startsWith(route + '/'));
  }, [pathname]);

  const isProtectedPath = React.useMemo(() => {
    if (isPublicPath) return false;
    return protectedRoutes.some(route => {
      if (pathname === route) return true;
      if (route.endsWith('*')) {
        const baseRoute = route.slice(0, -1);
        return pathname.startsWith(baseRoute);
      }
      return pathname.startsWith(route + '/');
    });
  }, [pathname, isPublicPath]);

  React.useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated && pathname === '/') {
        router.replace('/dashboard');
      } else if (!isAuthenticated && isProtectedPath) {
        router.replace('/login');
      }
    }
  }, [isLoading, isAuthenticated, pathname, isProtectedPath, router]);

  const LoadingUI = () => (
    <div className="flex h-screen w-screen bg-[url('/images/background_loading.jpg')] bg-no-repeat bg-cover bg-center items-center justify-center">
      <div className="flex h-screen w-screen backdrop-blur-md bg-[#080D27]/40 items-center justify-center transition-all duration-100">
        <div className="relative w-[300px] h-[300px]">
          <Spinner />
        </div>
      </div>
    </div>
  );

  if (
    isLoading ||
    (isProtectedPath && !isAuthenticated) ||
    (isAuthenticated && pathname === '/')
  ) {
    return <LoadingUI />;
  }

  return <>{children}</>;
}
