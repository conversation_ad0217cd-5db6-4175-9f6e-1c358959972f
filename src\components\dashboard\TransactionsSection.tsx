"use client"

import React, { useState } from 'react'
import { Input } from '@/components/core/Input'
import { Button } from '@/components/core/Button'
import TransactionsTable from './TransactionsTable'
import EmptyTransactions from './EmptyTransactions'

export default function TransactionsSection() {
  const [hasTransactions, setHasTransactions] = useState(false) // Set to true to show table with data
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-medium text-gray-900">Transactions</h2>
          <Button
            onClick={() => setHasTransactions(!hasTransactions)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-xs"
          >
            {hasTransactions ? 'Show Empty State' : 'Show Sample Data'}
          </Button>
        </div>

        {/* Search Input */}
        <div className="relative max-w-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-4 w-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <Input
            type="text"
            placeholder="Search transactions"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
          />
        </div>
      </div>

      {/* Transactions Content */}
      {hasTransactions ? (
        <TransactionsTable showData={true} />
      ) : (
        <EmptyTransactions />
      )}
    </div>
  )
}
