"use client"

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/core/Button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogBody,
  DialogClose,
} from '@/components/core/Dialog'
import { Input } from '@/components/core'
import MerchantsSuccess from './MerchantsSuccess'
import { useBooleanStateControl } from '@/hooks'

// Zod validation schema
const startEarningSchema = z.object({
  fullname: z.string().min(2, 'Full name must be at least 2 characters'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits').regex(/^\d+$/, 'Phone number must contain only digits'),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  bvn: z.string().min(11, 'BVN must be 11 digits').max(11, 'BVN must be 11 digits').regex(/^\d+$/, 'BVN must contain only digits'),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms of service')
})

type StartEarningFormData = z.infer<typeof startEarningSchema>

interface StartEarningModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function StartEarningModal({ isOpen, onClose }: StartEarningModalProps) {
  const {
    state: isMerchantSuccessodalOpen,
    setState: setMerchantSuccessodalState,
    setTrue: openMerchantSuccessodal,
  } = useBooleanStateControl();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset
  } = useForm<StartEarningFormData>({
    resolver: zodResolver(startEarningSchema)
  })

  const onSubmit = async (data: StartEarningFormData) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      console.log('Form submitted:', data)

      // Reset form and close modal on success
      reset()
      openMerchantSuccessodal()
      onClose()
    } catch (error) {
      console.error('Submission error:', error)
      // Handle error (show error message)
    }
  }

  const handleCancel = () => {
    reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-full max-w-lg mx-4 bg-white rounded-2xl border-0 p-0 max-h-[90vh] overflow-y-auto no-scrollbar"
        overlayClassName="bg-black/60 backdrop-blur-sm"
      >
        <div className="p-6">
          <DialogHeader className="text-right bg-transparent px-0 py-0">
            <DialogTitle>
            </DialogTitle>
            <DialogClose >
              <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="17" cy="17" r="17" fill="#F9FAFB" />
                <line x1="12.8201" y1="12.1123" x2="21.0141" y2="20.3063" stroke="black" />
                <line y1="-0.5" x2="11.5881" y2="-0.5" transform="matrix(-0.707107 0.707107 0.707107 0.707107 21.2463 12.4658)" stroke="black" />
              </svg>
            </DialogClose>
          </DialogHeader>

          <DialogBody className="px-0 py-0">
            <DialogTitle className="text-lg font-medium text-black mb-2 max-w-[393px]">
              Start Earning Passively as a Vendboss Merchant, Let your money works for you.
            </DialogTitle>
            <DialogDescription className="text-[#4A4A68] leading-[16px] text-xs max-w-[393px]">
              Earn commissions every time you or your customers buy airtime, pay electricity bills, or subscribe to cable TV. With Vendboss, you can grow your income effortlessly, right from your phone.
            </DialogDescription>
            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-6">
              {/* Full Name */}
              <div>
                <label htmlFor="fullname" className="block text-[13px] font-medium text-black mb-1">
                  Fullname
                </label>
                <Input
                  {...register('fullname')}
                  type="text"
                  id="fullname"
                  placeholder="Enter full name"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.fullname && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.fullname.message}</p>
                )}
              </div>

              {/* Phone Number */}
              <div>
                <label htmlFor="phoneNumber" className="block text-[13px] font-medium text-black mb-1">
                  Phone Number
                </label>
                <Input
                  {...register('phoneNumber')}
                  type="tel"
                  id="phoneNumber"
                  placeholder="Enter phone number"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.phoneNumber && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.phoneNumber.message}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-[13px] font-medium text-black mb-1">
                  Email
                </label>
                <Input
                  {...register('email')}
                  type="email"
                  id="email"
                  placeholder="Enter email address"
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.email && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.email.message}</p>
                )}
              </div>

              {/* BVN */}
              <div>
                <label htmlFor="bvn" className="block text-[13px] font-medium text-black mb-1">
                  BVN
                </label>
                <Input
                  {...register('bvn')}
                  type="text"
                  id="bvn"
                  placeholder="Enter BVN"
                  maxLength={11}
                  className="w-full px-3 py-2 border border-[#E9EBEE] text-xs text-black rounded-lg focus:ring-1 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200"
                />
                {errors.bvn && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.bvn.message}</p>
                )}
              </div>

              {/* Address */}
              <div>
                <label htmlFor="address" className="block text-[13px] font-medium text-black mb-1">
                  Address
                </label>
                <Input
                  {...register('address')}
                  id="address"
                  placeholder="Enter your address"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 text-black text-xs focus:ring-green-500 focus:border-transparent outline-none transition-all duration-200 resize-none"
                />
                {errors.address && (
                  <p className="text-red-500 text-[10px] mt-1">{errors.address.message}</p>
                )}
              </div>

              {/* Terms Checkbox */}
              <div className="flex items-center space-x-2">
                <Input
                  {...register('acceptTerms')}
                  type="checkbox"
                  id="acceptTerms"
                  className="rounded-full w-[14px] h-[14px] text-green-600 border-gray-300 focus:ring-green-500"
                />
                <label htmlFor="acceptTerms" className="text-xs text-gray-700">
                  Accept our <span className='text-[#105230]'>terms of service</span>
                </label>
              </div>
              {errors.acceptTerms && (
                <p className="text-red-500 text-[10px]">{errors.acceptTerms.message}</p>
              )}

              {/* Buttons */}
              <div className='mt-10 w-full h-[1px] bg-[#E9EBEE]'></div>
              <div className="flex space-x-3 pt-4">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-[#105230] hover:bg-green-700 text-white py-3 px-9 rounded-full font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Processing...' : 'Proceed'}
                </Button>
                <Button
                  type="button"
                  onClick={handleCancel}
                  className=" bg-[#E7EEEA] hover:bg-gray-300 text-[#105230] py-3 px-9 rounded-full font-medium transition-colors duration-200"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogBody>
        </div>
      </DialogContent>

      <MerchantsSuccess
        isMerchantSuccessOpen={isMerchantSuccessodalOpen}
        setMerchantSuccessModalState={setMerchantSuccessodalState}
      />
    </Dialog>
  )
}
